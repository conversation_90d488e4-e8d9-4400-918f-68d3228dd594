<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Model\Service;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Attribute\Frontend\Image as FrontendImage;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Psr\Log\LoggerInterface;

class ImageRoles
{
    /**
     * @var array<string>|null
     */
    private ?array $imageRolesCache = null;

    public function __construct(
        private readonly ProductRepositoryInterface $productRepository,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository,
        private readonly SearchCriteriaBuilder $searchCriteriaBuilder,
        private readonly LoggerInterface $logger
    ) {}

    final public function getImageRoles(string $sku, string $fileName): array
    {
        try {
            $product = $this->productRepository->get($sku);
        } catch (NoSuchEntityException) {
            throw new GraphQlNoSuchEntityException(__('No such product with sku %1', $sku));
        }

        $imageRoles = [];
        $imageRolesKeys = $this->getImageRolesAttributes();
        foreach ($imageRolesKeys as $imageRole) {
            if ($product->getCustomAttribute($imageRole['attribute_code'])?->getValue() === $fileName) {
                $imageRoles[] = $imageRole['attribute_code'];
            }
        }

        return $imageRoles;
    }

    private function getImageRolesAttributes(): array
    {
        if ($this->imageRolesCache === null) {
            try {
                $searchCriteria = $this->searchCriteriaBuilder
                    ->addFilter('frontend_model', FrontendImage::class)
                    ->addFilter('frontend_input', 'media_image')
                    ->create();

                $attributes = $this->productAttributeRepository->getList($searchCriteria)->getItems();
                $this->imageRolesCache = [];

                foreach ($attributes as $attribute) {
                    $this->imageRolesCache[] = ['attribute_code' => $attribute->getAttributeCode()];
                }
            } catch (\Exception $e) {
                $this->logger->error(
                    'Error getting image roles attributes',
                    [
                        'error' => $e->getMessage()
                    ]
                );
                $this->imageRolesCache = [];
            }
        }

        return $this->imageRolesCache;
    }
}
