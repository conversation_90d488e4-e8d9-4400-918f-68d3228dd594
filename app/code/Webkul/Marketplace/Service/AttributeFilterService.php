<?php

declare(strict_types=1);

namespace Webkul\Marketplace\Service;

use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Psr\Log\LoggerInterface;

/**
 * Service class to handle EAV attribute filtering for product collections
 */
class AttributeFilterService
{
    private const DEFAULT_STORE_ID = 0;

    /**
     * @var array<string, \Magento\Catalog\Api\Data\ProductAttributeInterface>
     */
    private array $attributeCache = [];

    public function __construct(
        private readonly ResourceConnection $resourceConnection,
        private readonly LoggerInterface $logger,
        private readonly ProductAttributeRepositoryInterface $productAttributeRepository
    ) {}

    /**
     * Apply EAV attribute filter to product collection
     *
     * @param Collection $collection
     * @param string $attributeCode
     * @param string $value
     * @return void
     * @throws LocalizedException
     */
    public function applyAttributeFilter(Collection $collection, string $attributeCode, string $value): void
    {
        $searchValue = trim($value, '%');
        if (empty($searchValue)) {
            return;
        }

        try {
            $attribute = $this->getAttribute($attributeCode);
            if (!$attribute) {
                $this->logger->info(
                    'Attribute not found for filtering',
                    ['attribute_code' => $attributeCode]
                );
                return;
            }

            $this->applyAttributeJoinAndFilter($collection, $attribute, $attributeCode, $searchValue);

        } catch (\Exception $e) {
            $this->logger->error(
                'Error applying attribute filter',
                [
                    'attribute_code' => $attributeCode,
                    'search_value' => $searchValue,
                    'error' => $e->getMessage()
                ]
            );
            throw new LocalizedException(__('Unable to apply filter for attribute: %1', $attributeCode));
        }
    }

    /**
     * Get attribute by attribute code with caching
     *
     * @param string $attributeCode
     * @return \Magento\Catalog\Api\Data\ProductAttributeInterface|null
     */
    private function getAttribute(string $attributeCode): ?\Magento\Catalog\Api\Data\ProductAttributeInterface
    {
        if (!isset($this->attributeCache[$attributeCode])) {
            try {
                $this->attributeCache[$attributeCode] = $this->productAttributeRepository->get($attributeCode);
            } catch (NoSuchEntityException $e) {
                $this->attributeCache[$attributeCode] = null;
            }
        }

        return $this->attributeCache[$attributeCode];
    }

    /**
     * Apply join and filter conditions to collection
     *
     * @param Collection $collection
     * @param \Magento\Catalog\Api\Data\ProductAttributeInterface $attribute
     * @param string $attributeCode
     * @param string $searchValue
     * @return void
     */
    private function applyAttributeJoinAndFilter(
        Collection $collection,
        \Magento\Catalog\Api\Data\ProductAttributeInterface $attribute,
        string $attributeCode,
        string $searchValue
    ): void {
        $connection = $this->resourceConnection->getConnection('read');
        $attributeTableName = $attribute->getBackendTable();
        $joinCondition = $this->buildJoinCondition($attribute->getAttributeId(), $attributeCode);
        $aliasName = $attributeCode . '_attr';

        $collection->getSelect()->joinLeft(
            [$aliasName => $attributeTableName],
            $joinCondition,
            []
        );

        $collection->getSelect()->where(
            $connection->quoteInto($aliasName . '.value = ?', $searchValue)
        );
    }

    /**
     * Build join condition based on whether the product entity table has row_id
     *
     * @param int $attributeId
     * @param string $attributeCode
     * @return string
     */
    private function buildJoinCondition(int $attributeId, string $attributeCode): string
    {
        $connection = $this->resourceConnection->getConnection('read');
        $productEntityTable = $connection->getTableName('catalog_product_entity');
        $hasRowId = $connection->tableColumnExists($productEntityTable, 'row_id');
        $aliasName = $attributeCode . '_attr';

        if ($hasRowId) {
            return sprintf(
                'e.row_id = %s.row_id AND %s.attribute_id = %d AND %s.store_id = %d',
                $aliasName,
                $aliasName,
                $attributeId,
                $aliasName,
                self::DEFAULT_STORE_ID
            );
        }

        return sprintf(
            'e.entity_id = %s.entity_id AND %s.attribute_id = %d AND %s.store_id = %d',
            $aliasName,
            $aliasName,
            $attributeId,
            $aliasName,
            self::DEFAULT_STORE_ID
        );
    }
}
