diff --git a/app/code/Webkul/Marketplace/Service/AttributeFilterService.php b/app/code/Webkul/Marketplace/Service/AttributeFilterService.php
deleted file mode 100644
index ac196b9c3..000000000
--- a/app/code/Webkul/Marketplace/Service/AttributeFilterService.php
+++ /dev/null
@@ -1,207 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace Webkul\Marketplace\Service;
-
-use Magento\Catalog\Model\ResourceModel\Product\Collection;
-use Magento\Framework\App\ResourceConnection;
-use Magento\Framework\DB\Adapter\AdapterInterface;
-use Magento\Framework\Exception\LocalizedException;
-use Psr\Log\LoggerInterface;
-
-/**
- * Service class to handle EAV attribute filtering for product collections
- */
-class AttributeFilterService
-{
-    private const PRODUCT_ENTITY_TYPE_ID = 4;
-    private const DEFAULT_STORE_ID = 0;
-
-    public function __construct(
-        private readonly ResourceConnection $resourceConnection,
-        private readonly LoggerInterface $logger
-    ) {}
-
-    /**
-     * Apply EAV attribute filter to product collection
-     *
-     * @param Collection $collection
-     * @param string $attributeCode
-     * @param string $searchValue
-     * @return void
-     * @throws LocalizedException
-     */
-    public function applyAttributeFilter(Collection $collection, string $attributeCode, string $value): void
-    {
-        $searchValue = trim($value, '%');
-        if (empty($searchValue)) {
-            return;
-        }
-
-        try {
-            $connection = $this->getConnection();
-            $attributeId = $this->getAttributeId($attributeCode, $connection);
-
-            if (!$attributeId) {
-                $this->logger->info(
-                    'Attribute not found for filtering',
-                    ['attribute_code' => $attributeCode]
-                );
-                return;
-            }
-
-            $this->applyAttributeJoinAndFilter($collection, $attributeId, $attributeCode, $searchValue, $connection);
-
-        } catch (\Exception $e) {
-            $this->logger->error(
-                'Error applying attribute filter',
-                [
-                    'attribute_code' => $attributeCode,
-                    'search_value' => $searchValue,
-                    'error' => $e->getMessage()
-                ]
-            );
-            throw new LocalizedException(__('Unable to apply filter for attribute: %1', $attributeCode));
-        }
-    }
-
-    /**
-     * Get database connection
-     *
-     * @return AdapterInterface
-     */
-    private function getConnection(): AdapterInterface
-    {
-        return $this->resourceConnection->getConnection();
-    }
-
-    /**
-     * Get attribute ID by attribute code
-     *
-     * @param string $attributeCode
-     * @param AdapterInterface $connection
-     * @return int|null
-     */
-    private function getAttributeId(string $attributeCode, AdapterInterface $connection): ?int
-    {
-        $eavAttributeTable = $connection->getTableName('eav_attribute');
-
-        $select = $connection->select()
-            ->from(['ea' => $eavAttributeTable], ['attribute_id'])
-            ->where('ea.entity_type_id = ?', self::PRODUCT_ENTITY_TYPE_ID)
-            ->where('ea.attribute_code = ?', $attributeCode);
-
-        $attributeId = $connection->fetchOne($select);
-
-        return $attributeId ? (int)$attributeId : null;
-    }
-
-    /**
-     * Apply join and filter conditions to collection
-     *
-     * @param Collection $collection
-     * @param int $attributeId
-     * @param string $attributeCode
-     * @param string $searchValue
-     * @param AdapterInterface $connection
-     * @return void
-     */
-    private function applyAttributeJoinAndFilter(
-        Collection $collection,
-        int $attributeId,
-        string $attributeCode,
-        string $searchValue,
-        AdapterInterface $connection
-    ): void {
-        $attributeTableName = $this->getAttributeTableName($connection, $attributeId);
-        $joinCondition = $this->buildJoinCondition($attributeId, $attributeCode, $connection);
-        $aliasName = $attributeCode . '_attr';
-
-        $collection->getSelect()->joinLeft(
-            [$aliasName => $attributeTableName],
-            $joinCondition,
-            []
-        );
-
-        $collection->getSelect()->where(
-            $connection->quoteInto($aliasName . '.value = ?', $searchValue)
-        );
-    }
-
-    /**
-     * Get the appropriate EAV attribute table name based on backend type
-     *
-     * @param AdapterInterface $connection
-     * @param int $attributeId
-     * @return string
-     */
-    private function getAttributeTableName(AdapterInterface $connection, int $attributeId): string
-    {
-        $backendType = $this->getAttributeBackendType($connection, $attributeId);
-
-        return match ($backendType) {
-            'int' => $connection->getTableName('catalog_product_entity_int'),
-            'decimal' => $connection->getTableName('catalog_product_entity_decimal'),
-            'text' => $connection->getTableName('catalog_product_entity_text'),
-            'datetime' => $connection->getTableName('catalog_product_entity_datetime'),
-            default => $connection->getTableName('catalog_product_entity_varchar'),
-        };
-    }
-
-    /**
-     * Get attribute backend type by attribute ID
-     *
-     * @param AdapterInterface $connection
-     * @param int $attributeId
-     * @return string
-     */
-    private function getAttributeBackendType(AdapterInterface $connection, int $attributeId): string
-    {
-        $eavAttributeTable = $connection->getTableName('eav_attribute');
-
-        $select = $connection->select()
-            ->from(['ea' => $eavAttributeTable], ['backend_type'])
-            ->where('ea.attribute_id = ?', $attributeId);
-
-        $backendType = $connection->fetchOne($select);
-
-        return $backendType ?: 'varchar';
-    }
-
-    /**
-     * Build join condition based on whether the product entity table has row_id
-     *
-     * @param int $attributeId
-     * @param string $attributeCode
-     * @param AdapterInterface $connection
-     * @return string
-     */
-    private function buildJoinCondition(int $attributeId, string $attributeCode, AdapterInterface $connection): string
-    {
-        $productEntityTable = $connection->getTableName('catalog_product_entity');
-        $columns = $connection->describeTable($productEntityTable);
-        $hasRowId = isset($columns['row_id']);
-        $aliasName = $attributeCode . '_attr';
-
-        if ($hasRowId) {
-            return sprintf(
-                'e.row_id = %s.row_id AND %s.attribute_id = %d AND %s.store_id = %d',
-                $aliasName,
-                $aliasName,
-                $attributeId,
-                $aliasName,
-                self::DEFAULT_STORE_ID
-            );
-        }
-
-        return sprintf(
-            'e.entity_id = %s.entity_id AND %s.attribute_id = %d AND %s.store_id = %d',
-            $aliasName,
-            $aliasName,
-            $attributeId,
-            $aliasName,
-            self::DEFAULT_STORE_ID
-        );
-    }
-}
diff --git a/app/code/Webkul/Marketplace/Ui/DataProvider/ProductListDataProvider.php b/app/code/Webkul/Marketplace/Ui/DataProvider/ProductListDataProvider.php
index 496aeaed0..361d79483 100755
--- a/app/code/Webkul/Marketplace/Ui/DataProvider/ProductListDataProvider.php
+++ b/app/code/Webkul/Marketplace/Ui/DataProvider/ProductListDataProvider.php
@@ -1,7 +1,4 @@
 <?php
-
-declare(strict_types=1);
-
 /**
  * Webkul Software.
  *
@@ -14,10 +11,8 @@ declare(strict_types=1);
 namespace Webkul\Marketplace\Ui\DataProvider;
 
 use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory as ProductCollection;
-use Magento\Framework\Registry;
 use Webkul\Marketplace\Model\ResourceModel\Product\CollectionFactory;
 use Webkul\Marketplace\Helper\Data as HelperData;
-use Webkul\Marketplace\Service\AttributeFilterService;
 
 /**
  * Class Product Collection data provider
@@ -27,7 +22,6 @@ class ProductListDataProvider extends \Magento\Catalog\Ui\DataProvider\Product\P
     public const PRODUCT_STATUS = 'product_status';
     public const APPROVED_STATUS = 'is_approved';
     public const PRODUCT_CATEGORY = 'category_id';
-    public const EAN_ATTRIBUTE = 'ean';
 
 
     /**
@@ -37,7 +31,10 @@ class ProductListDataProvider extends \Magento\Catalog\Ui\DataProvider\Product\P
      */
     protected $collection;
 
-
+    /**
+     * @var \Magento\Framework\Registry
+     */
+    protected $_registry;
 
    /**
     * Construct
@@ -46,6 +43,9 @@ class ProductListDataProvider extends \Magento\Catalog\Ui\DataProvider\Product\P
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param ProductCollection $productCollection
+    * @param CollectionFactory $collectionFactory
+    * @param HelperData $helperData
+    * @param \Magento\Framework\Registry $registry
     * @param array $addFieldStrategies
     * @param array $addFilterStrategies
     * @param array $meta
@@ -56,10 +56,9 @@ class ProductListDataProvider extends \Magento\Catalog\Ui\DataProvider\Product\P
         $primaryFieldName,
         $requestFieldName,
         ProductCollection $productCollection,
-        private readonly CollectionFactory $collectionFactory,
-        private readonly HelperData $helperData,
-        private readonly Registry $registry,
-        private readonly AttributeFilterService $attributeFilterService,
+        CollectionFactory $collectionFactory,
+        HelperData $helperData,
+        \Magento\Framework\Registry $registry,
         array $addFieldStrategies = [],
         array $addFilterStrategies = [],
         array $meta = [],
@@ -76,18 +75,17 @@ class ProductListDataProvider extends \Magento\Catalog\Ui\DataProvider\Product\P
             $data
         );
 
-        $sellerId = $this->helperData->getCustomerId();
-        if (!$this->registry->registry('mp_flat_catalog_flag')) {
-            $this->registry->register('mp_flat_catalog_flag', 1);
+        $sellerId = $helperData->getCustomerId();
+        if (!$registry->registry('mp_flat_catalog_flag')) {
+            $registry->register('mp_flat_catalog_flag', 1);
         }
-        $marketplaceTable = $this->collectionFactory->create()->getTable('marketplace_product');
-        $marketplaceProduct = $this->collectionFactory->create()
+        $marketplaceTable = $collectionFactory->create()->getTable('marketplace_product');
+        $marketplaceProduct = $collectionFactory->create()
         ->addFieldToFilter('seller_id', $sellerId);
         $allIds = $marketplaceProduct->getAllIds();
         /** @var Collection $collection */
         $collectionData = $productCollection->create();
         $collectionData->addAttributeToSelect('status');
-        $collectionData->addAttributeToSelect('ean');
 
         $collectionData->getSelect()->join(
             $marketplaceTable.' as cgf',
@@ -130,12 +128,6 @@ class ProductListDataProvider extends \Magento\Catalog\Ui\DataProvider\Product\P
 
             $this->getCollection()->getSelect()->where($this->getCollection()->getConnection()->prepareSqlCondition('e.entity_id', ['in' => $selectCondition]));
 
-        } elseif ($filter->getField() == self::EAN_ATTRIBUTE) {
-            $this->attributeFilterService->applyAttributeFilter(
-                $this->getCollection(),
-                self::EAN_ATTRIBUTE,
-                (string)$filter->getValue()
-            );
         } else {
             parent::addFilter($filter);
         }
diff --git a/app/code/Webkul/Marketplace/view/frontend/ui_component/marketplace_products_listing.xml b/app/code/Webkul/Marketplace/view/frontend/ui_component/marketplace_products_listing.xml
index 53c936887..eacbeb39d 100755
--- a/app/code/Webkul/Marketplace/view/frontend/ui_component/marketplace_products_listing.xml
+++ b/app/code/Webkul/Marketplace/view/frontend/ui_component/marketplace_products_listing.xml
@@ -229,16 +229,6 @@
                 </item>
             </argument>
         </column>
-        <column name="ean">
-            <argument name="data" xsi:type="array">
-                <item name="config" xsi:type="array">
-                    <item name="filter" xsi:type="string">text</item>
-                    <item name="add_field" xsi:type="boolean">true</item>
-                    <item name="label" xsi:type="string" translate="true">EAN</item>
-                    <item name="sortOrder" xsi:type="number">71</item>
-                </item>
-            </argument>
-        </column>
         <column name="qty">
             <argument name="data" xsi:type="array">
                 <item name="config" xsi:type="array">
